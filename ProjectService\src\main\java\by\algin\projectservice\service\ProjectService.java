package by.algin.projectservice.service;

import by.algin.dto.project.*;
import by.algin.dto.response.PagedResponse;
import by.algin.dto.user.UserResponse;
import by.algin.projectservice.enums.Permission;
import by.algin.projectservice.enums.ProjectRole;
import by.algin.projectservice.entity.Project;
import by.algin.projectservice.entity.ProjectMember;
import by.algin.projectservice.exception.ProjectAccessDeniedException;
import by.algin.projectservice.exception.ProjectNotFoundException;
import by.algin.projectservice.exception.ProjectMemberNotFoundException;
import by.algin.projectservice.repository.ProjectRepository;
import by.algin.projectservice.repository.ProjectMemberRepository;
import by.algin.projectservice.util.UserBaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectService {

    private final ProjectRepository projectRepository;
    private final ProjectMemberRepository memberRepository;
    private final ProjectSecurityService securityService;
    private final UserBaseService userBaseService;

    @Transactional
    public ProjectResponse createProject(CreateProjectRequest request, Long ownerId) {
        log.info("Creating project '{}' for user {}", request.getName(), ownerId);

        Project project = Project.builder()
                .name(request.getName())
                .description(request.getDescription())
                .ownerId(ownerId)
                .build();

        project = projectRepository.save(project);

        ProjectMember ownerMember = ProjectMember.builder()
                .project(project)
                .userId(ownerId)
                .role(ProjectRole.OWNER)
                .build();

        memberRepository.save(ownerMember);

        log.info("Project '{}' created successfully with ID: {}", project.getName(), project.getId());
        return convertToProjectResponse(project);
    }

    public ProjectResponse getProjectById(Long projectId, Long userId) {
        log.debug("Getting project {} for user {}", projectId, userId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));

        securityService.requireViewAccess(projectId, userId);

        return convertToProjectResponse(project);
    }

    @Transactional
    public ProjectResponse updateProject(Long projectId, UpdateProjectRequest request, Long userId) {
        log.info("Updating project {} by user {}", projectId, userId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));

        securityService.requireEditAccess(projectId, userId);

        if (request.getName() != null) {
            project.setName(request.getName());
        }
        if (request.getDescription() != null) {
            project.setDescription(request.getDescription());
        }

        project = projectRepository.save(project);

        log.info("Project {} updated successfully", projectId);
        return convertToProjectResponse(project);
    }

    @Transactional
    public void deleteProject(Long projectId, Long userId) {
        log.info("Deleting project {} by user {}", projectId, userId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));

        securityService.requireDeleteAccess(projectId, userId);

        projectRepository.delete(project);

        log.info("Project {} deleted successfully", projectId);
    }

    private ProjectResponse convertToProjectResponse(Project project) {
        return ProjectResponse.builder()
                .id(project.getId())
                .name(project.getName())
                .description(project.getDescription())
                .status(by.algin.dto.project.ProjectStatus.valueOf(project.getStatus().name()))
                .ownerId(project.getOwnerId())
                .createdAt(project.getCreatedAt())
                .updatedAt(project.getUpdatedAt())
                .build();
    }

    @Transactional
    public ProjectMemberResponse addProjectMember(Long projectId, AddProjectMemberRequest request, Long currentUserId) {
        log.info("Adding user {} to project {} with role {} by user {}",
                request.getUserId(), projectId, request.getRole(), currentUserId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));

        securityService.requireMemberManagementAccess(projectId, currentUserId);

        Long userId = request.getUserId();
        ProjectRole role = ProjectRole.valueOf(request.getRole().name());

        securityService.requireRoleAssignmentAccess(projectId, currentUserId, role);

        if (memberRepository.existsByProjectIdAndUserId(projectId, userId)) {
            throw new IllegalArgumentException("User is already a member of this project");
        }

        UserResponse user = userBaseService.getUserByIdOrThrow(userId);

        ProjectMember member = ProjectMember.builder()
                .project(project)
                .userId(userId)
                .role(role)
                .build();

        member = memberRepository.save(member);

        log.info("User {} added to project {} successfully", userId, projectId);
        return convertToProjectMemberResponse(member, user);
    }

    @Transactional
    public ProjectMemberResponse updateMemberRole(Long projectId, Long userId,
                                                 UpdateProjectMemberRoleRequest request, Long currentUserId) {
        log.info("Updating role of user {} in project {} to {} by user {}",
                userId, projectId, request.getRole(), currentUserId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));

        securityService.requireMemberManagementAccess(projectId, currentUserId);

        ProjectMember member = memberRepository.findByProjectIdAndUserId(projectId, userId)
                .orElseThrow(() -> new ProjectMemberNotFoundException(projectId, userId));

        ProjectRole newRole = ProjectRole.valueOf(request.getRole().name());

        securityService.requireRoleAssignmentAccess(projectId, currentUserId, newRole);

        if (member.getRole() == ProjectRole.OWNER) {
            throw new IllegalArgumentException("Cannot change role of project owner");
        }

        if (newRole == ProjectRole.OWNER) {
            throw new IllegalArgumentException("Cannot promote member to owner role");
        }

        member.setRole(newRole);
        member = memberRepository.save(member);

        UserResponse user = userBaseService.getUserByIdOrThrow(userId);

        log.info("Role of user {} in project {} updated successfully", userId, projectId);
        return convertToProjectMemberResponse(member, user);
    }

    public void removeProjectMember(Long projectId, Long userId, Long currentUserId) {
        log.info("Removing user {} from project {} by user {}", userId, projectId, currentUserId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));

        securityService.requireMemberManagementAccess(projectId, currentUserId);

        ProjectMember member = memberRepository.findByProjectIdAndUserId(projectId, userId)
                .orElseThrow(() -> new ProjectMemberNotFoundException(projectId, userId));

        if (member.getRole() == ProjectRole.OWNER) {
            throw new IllegalArgumentException("Cannot remove project owner");
        }

        memberRepository.delete(member);

        log.info("User {} removed from project {} successfully", userId, projectId);
    }

    public PagedResponse<ProjectMemberResponse> getProjectMembers(Long projectId, Long userId, Pageable pageable) {
        log.debug("Getting members for project {} by user {}", projectId, userId);

        securityService.requireViewAccess(projectId, userId);

        Page<ProjectMember> membersPage = memberRepository.findByProjectId(projectId, pageable);

        List<Long> userIds = membersPage.getContent().stream()
                .map(ProjectMember::getUserId)
                .collect(Collectors.toList());

        Map<Long, UserResponse> users = userBaseService.getUsersByIdsAsMap(userIds);

        List<ProjectMemberResponse> memberResponses = membersPage.getContent().stream()
                .map(member -> {
                    UserResponse user = users.get(member.getUserId());
                    if (user == null) {
                        log.warn("User data not found for user ID: {} in project {}", member.getUserId(), projectId);
                        throw new IllegalStateException("User data not found for user ID: " + member.getUserId());
                    }
                    return convertToProjectMemberResponse(member, user);
                })
                .collect(Collectors.toList());

        return PagedResponse.<ProjectMemberResponse>builder()
                .content(memberResponses)
                .page(membersPage.getNumber())
                .size(membersPage.getSize())
                .totalElements(membersPage.getTotalElements())
                .totalPages(membersPage.getTotalPages())
                .first(membersPage.isFirst())
                .last(membersPage.isLast())
                .build();
    }

    public PagedResponse<ProjectResponse> getUserProjects(Long userId, Pageable pageable) {
        log.debug("Getting projects for user {} with pagination", userId);

        Page<ProjectMember> membersPage = memberRepository.findByUserId(userId, pageable);

        List<ProjectResponse> projectResponses = membersPage.getContent().stream()
                .map(member -> convertToProjectResponse(member.getProject()))
                .collect(Collectors.toList());

        return PagedResponse.<ProjectResponse>builder()
                .content(projectResponses)
                .page(membersPage.getNumber())
                .size(membersPage.getSize())
                .totalElements(membersPage.getTotalElements())
                .totalPages(membersPage.getTotalPages())
                .first(membersPage.isFirst())
                .last(membersPage.isLast())
                .build();
    }

    private ProjectMemberResponse convertToProjectMemberResponse(ProjectMember member, UserResponse user) {
        if (user == null) {
            throw new IllegalStateException("User data not found for user ID: " + member.getUserId());
        }

        return ProjectMemberResponse.builder()
                .id(member.getId())
                .projectId(member.getProject().getId())
                .userId(member.getUserId())
                .username(user.getUsername())
                .email(user.getEmail())
                .role(by.algin.dto.project.ProjectRole.valueOf(member.getRole().name()))
                .joinedAt(member.getJoinedAt())
                .build();
    }
}
