package by.algin.projectservice.repository;

import by.algin.enums.ProjectRole;
import by.algin.projectservice.entity.ProjectMember;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProjectMemberRepository extends JpaRepository<ProjectMember, Long> {

    Optional<ProjectMember> findByProjectIdAndUserId(Long projectId, Long userId);

    List<ProjectMember> findByProjectId(Long projectId);

    List<ProjectMember> findByUserId(Long userId);

    boolean existsByProjectIdAndUserId(Long projectId, Long userId);

    void deleteByProjectIdAndUserId(Long projectId, Long userId);

    List<ProjectMember> findByProjectIdAndRole(Long projectId, ProjectRole role);

    Page<ProjectMember> findByProjectId(Long projectId, Pageable pageable);

    Page<ProjectMember> findByUserId(Long userId, Pageable pageable);

    Page<ProjectMember> findByProjectIdAndRole(Long projectId, ProjectRole role, Pageable pageable);

    long countByProjectId(Long projectId);

    long countByUserId(Long userId);

    @Query("SELECT pm FROM ProjectMember pm WHERE pm.project.id = :projectId AND pm.role IN :managerRoles ORDER BY pm.role DESC, pm.joinedAt ASC")
    List<ProjectMember> findProjectManagers(@Param("projectId") Long projectId, @Param("managerRoles") List<ProjectRole> managerRoles);

    List<ProjectMember> findByProjectIdAndUserIdIn(Long projectId, List<Long> userIds);

    @Query("SELECT pm.project.id, COUNT(pm) FROM ProjectMember pm WHERE pm.project.id IN :projectIds GROUP BY pm.project.id")
    List<Object[]> countMembersByProjectIds(@Param("projectIds") List<Long> projectIds);

    List<ProjectMember> findByProjectIdOrderByJoinedAtAsc(Long projectId);

    Page<ProjectMember> findByProjectIdOrderByJoinedAtAsc(Long projectId, Pageable pageable);

    List<ProjectMember> findByProjectIdOrderByJoinedAtDesc(Long projectId);

    Page<ProjectMember> findByProjectIdOrderByJoinedAtDesc(Long projectId, Pageable pageable);

    List<ProjectMember> findByProjectIdOrderByRoleAscJoinedAtAsc(Long projectId);

    @Query("SELECT pm FROM ProjectMember pm WHERE pm.project.id = :projectId AND pm.role IN :managerRoles")
    Page<ProjectMember> findProjectManagersPageable(@Param("projectId") Long projectId, @Param("managerRoles") List<ProjectRole> managerRoles, Pageable pageable);

    Page<ProjectMember> findByProjectIdAndUserIdIn(Long projectId, List<Long> userIds, Pageable pageable);

    default List<ProjectMember> findProjectManagers(Long projectId) {
        return findProjectManagers(projectId, List.of(ProjectRole.OWNER, ProjectRole.MANAGER));
    }

    default Page<ProjectMember> findProjectManagersPageable(Long projectId, Pageable pageable) {
        return findProjectManagersPageable(projectId, List.of(ProjectRole.OWNER, ProjectRole.MANAGER), pageable);
    }
}
