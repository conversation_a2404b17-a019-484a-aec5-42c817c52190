package by.algin.projectservice.config;

import by.algin.constants.CommonProjectConstants;
import by.algin.dto.project.ProjectStatus;
import by.algin.projectservice.constants.ProjectMessageConstants;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

@Data
@Component
@ConfigurationProperties(prefix = "app")
@Validated
public class AppProperties {

    @Valid
    @NotNull
    private final Project project = new Project();

    @Valid
    @NotNull
    private final Invitation invitation = new Invitation();

    @Valid
    @NotNull
    private final Pagination pagination = new Pagination();

    @Valid
    @NotNull
    private final Fallback fallback = new Fallback();

    @Valid
    @NotNull
    private final StatusTransitions statusTransitions = new StatusTransitions();





    @Data
    public static class Project {
        @Valid
        @NotNull
        private final Validation validation = new Validation();

        @Valid
        @NotNull
        private final Limits limits = new Limits();

        @Data
        public static class Validation {
            @NotNull
            @Min(1)
            private Integer minNameLength;

            @NotNull
            @Min(1)
            private Integer maxNameLength;

            @NotNull
            @Min(1)
            private Integer maxDescriptionLength;
        }

        @Data
        public static class Limits {
            @NotNull
            @Min(1)
            private Integer maxProjectsPerUser;

            @NotNull
            @Min(1)
            private Integer maxMembersPerProject;
        }
    }

    @Data
    public static class Invitation {
        @NotNull
        @Min(1)
        private Integer expiryHours;

        @NotNull
        @Min(1)
        private Integer tokenLength;
    }

    @Data
    public static class Pagination {
        @NotNull
        @Min(0)
        private Integer defaultPage = 0;

        @NotNull
        @Min(1)
        private Integer defaultPageSize = 20;

        @NotNull
        @Min(1)
        private Integer maxPageSize = 100;

        @NotNull
        private String defaultSortBy = "createdAt";

        @NotNull
        private String defaultSortDirection = "desc";

        @Valid
        @NotNull
        private final Members members = new Members();

        @Data
        public static class Members {
            @NotNull
            private String defaultSortBy = "joinedAt";

            @NotNull
            private String defaultSortDirection = "asc";
        }
    }

    @Data
    public static class Fallback {
        @NotNull
        private String usernamePrefix;

        @NotNull
        private String emailSuffix;
    }

    @Data
    public static class Role {
        @Valid
        @NotNull
        private final Levels levels = new Levels();

        @Valid
        @NotNull
        private final Names names = new Names();

        @Data
        public static class Levels {
            @NotNull
            @Min(1)
            private Integer viewer;

            @NotNull
            @Min(1)
            private Integer developer;

            @NotNull
            @Min(1)
            private Integer manager;

            @NotNull
            @Min(1)
            private Integer owner;
        }

        @Data
        public static class Names {
            @NotNull
            private String owner = CommonProjectConstants.ROLE_OWNER;

            @NotNull
            private String manager = CommonProjectConstants.ROLE_MANAGER;

            @NotNull
            private String developer = CommonProjectConstants.ROLE_DEVELOPER;

            @NotNull
            private String viewer = CommonProjectConstants.ROLE_VIEWER;
        }
    }

    @Data
    @Slf4j
    public static class StatusTransitions {
        @NotNull
        private Map<String, String> transitions = Map.of(
            CommonProjectConstants.STATUS_ACTIVE, CommonProjectConstants.STATUS_ARCHIVED + "," + CommonProjectConstants.STATUS_COMPLETED + "," + CommonProjectConstants.STATUS_INACTIVE,
            CommonProjectConstants.STATUS_ARCHIVED, CommonProjectConstants.STATUS_ACTIVE,
            CommonProjectConstants.STATUS_COMPLETED, CommonProjectConstants.STATUS_ARCHIVED,
            CommonProjectConstants.STATUS_INACTIVE, CommonProjectConstants.STATUS_ACTIVE + "," + CommonProjectConstants.STATUS_ARCHIVED
        );

public Set<ProjectStatus> getTransitionsFor(ProjectStatus currentStatus) {
            if (currentStatus == null) {
                return Set.of();
            }

            String transitionsString = transitions.get(currentStatus.name());
            return parseTransitions(transitionsString);
        }

        public Map<ProjectStatus, Set<ProjectStatus>> getAllTransitions() {
            Map<ProjectStatus, Set<ProjectStatus>> result = new java.util.HashMap<>();

            for (Map.Entry<String, String> entry : transitions.entrySet()) {
                ProjectStatus fromStatus = parseProjectStatus(entry.getKey());
                if (fromStatus != null) {
                    Set<ProjectStatus> toStatuses = parseTransitions(entry.getValue());
                    result.put(fromStatus, toStatuses);
                }
            }

            return result;
        }

        private ProjectStatus parseProjectStatus(String statusName) {
            try {
                return ProjectStatus.valueOf(statusName);
            } catch (IllegalArgumentException e) {
                log.warn(ProjectMessageConstants.INVALID_STATUS_IN_CONFIGURATION, statusName);
                return null;
            }
        }

        public boolean isTransitionAllowed(ProjectStatus from, ProjectStatus to) {
            return getTransitionsFor(from).contains(to);
        }

        private Set<ProjectStatus> parseTransitions(String transitionsString) {
            if (transitionsString == null || transitionsString.trim().isEmpty()) {
                return Set.of();
            }

            Set<ProjectStatus> result = new java.util.HashSet<>();
            String[] statusNames = transitionsString.split(",");

            for (String statusName : statusNames) {
                String trimmedName = statusName.trim();
                if (!trimmedName.isEmpty()) {
                    ProjectStatus status = parseProjectStatusForTransition(trimmedName);
                    if (status != null) {
                        result.add(status);
                    }
                }
            }

            return result;
        }

        private ProjectStatus parseProjectStatusForTransition(String statusName) {
            try {
                return ProjectStatus.valueOf(statusName);
            } catch (IllegalArgumentException e) {
                log.warn(ProjectMessageConstants.INVALID_STATUS_IN_TRANSITIONS_CONFIGURATION, statusName);
                return null;
            }
        }
    }


}
